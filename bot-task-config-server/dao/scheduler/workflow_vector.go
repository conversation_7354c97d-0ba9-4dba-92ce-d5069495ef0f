package scheduler

import (
	"context"
	"encoding/hex"
	"fmt"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/galileo/trpc-go-galileo/logs"
	"go.opentelemetry.io/otel/trace"
)

// setTraceID 设置traceID到context中
func setTraceID(ctx context.Context, traceID string) context.Context {
	ctx, msg := codec.EnsureMessage(ctx)

	logger, ok := codec.Message(ctx).Logger().(log.Logger)
	if !ok {
		logger = log.GetDefaultLogger()
	}
	logger = logs.WithSpanField(logger, ctx, traceID, "", false)
	msg.WithLogger(logger)

	traceIDByte := trace.TraceID{}
	dBytes, err := hex.DecodeString(traceID)
	if err != nil || len(dBytes) != len(traceIDByte) {
		return ctx
	}
	copy(traceIDByte[:], dBytes)
	ctx = trace.ContextWithSpanContext(ctx, trace.NewSpanContext(trace.SpanContextConfig{TraceID: traceIDByte}))
	return ctx
}

// NewUpgradeWorkflowVectorTask 创建升级工作流embedding向量任务
func NewUpgradeWorkflowVectorTask(ctx context.Context, param entity.TaskUpgradeWorkflowVectorParams) error {
	log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask, appID: %s, params: %+v", param.RobotID, param)

	// 将 RequestID 设置到 context 中作为 traceID
	ctx = trpc.CloneContext(ctx)
	ctx = setTraceID(ctx, param.RequestID)

	task, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(param.RobotID))), entity.TaskUpgradeWorkflowVector,
		entity.TaskMutexNone, param)
	task.TraceID = param.RequestID
	taskID, err := taskScheduler.CreateTask(ctx, task)
	if err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed, appID: %s, task: %+v, err: %+v", param.RobotID, task, err)
		return err
	}
	log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask success, appID: %s, taskID: %d", param.RobotID, taskID)
	return nil
}

// NewUpgradeWorkflowVectorTasks 创建升级工作流embedding向量任务
func NewUpgradeWorkflowVectorTasks(ctx context.Context, params []entity.TaskUpgradeWorkflowVectorParams) ([]string, []string, []error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	succeededAppIDs := make([]string, 0)
	failedAppIDs := make([]string, 0)
	failedErrors := make([]error, 0)
	for _, param := range params {
		wg.Add(1)
		go func(p entity.TaskUpgradeWorkflowVectorParams) {
			defer wg.Done()
			log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask, appID: %s, params: %+v", p.RobotID, p)

			// 将 RequestID 设置到 context 中作为 traceID
			taskCtx := trpc.CloneContext(ctx)
			taskCtx = setTraceID(taskCtx, p.RequestID)

			task, _ := task_scheduler.NewTask(
				taskCtx, task_scheduler.UserID(uint64(encode.StringToInt64(p.RobotID))), entity.TaskUpgradeWorkflowVector,
				entity.TaskMutexNone, p,
			)
			task.TraceID = p.RequestID
			taskID, err := taskScheduler.CreateTask(taskCtx, task)
			if err != nil {
				log.ErrorContextf(ctx, "taskScheduler.CreateTask failed, appID: %s, task: %+v, err: %+v", p.RobotID, task, err)
				mu.Lock()
				failedAppIDs = append(failedAppIDs, p.RobotID)
				failedErrors = append(failedErrors, err)
				mu.Unlock()
				return
			}
			log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask success, appID: %s, taskID: %d", p.RobotID, taskID)
			mu.Lock()
			succeededAppIDs = append(succeededAppIDs, p.RobotID)
			mu.Unlock()
		}(param)
	}
	wg.Wait()
	return succeededAppIDs, failedAppIDs, failedErrors
}

// GetUpgradeWorkflowVectorTasks 获取升级工作流embedding向量任务列表
func GetUpgradeWorkflowVectorTasks(ctx context.Context, pageSize, pageNum int) ([]*entity.SchedulerTask, int64, error) {
	// task_scheduler 根据AppID查询进行中的向量升级任务
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks, pageSize:%d, pageNum:%d", pageSize, pageNum)
	query := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.Type)
	table := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName())
	var totalCount int64
	err := table.Count(&totalCount).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Count err: %+v", err)
		return nil, 0, err
	}
	var tasks []*entity.SchedulerTask
	offset := (pageNum - 1) * pageSize
	order := fmt.Sprintf("%s DESC", entity.TSchedulerTaskColumns.CreateTime)
	err = table.
		Where(query, entity.TaskUpgradeWorkflowVector).
		Order(order).
		Offset(offset).
		Limit(pageSize).
		Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Find err: %+v", err)
		return nil, 0, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks success, total: %d, len(tasks): %d", totalCount, len(tasks))
	// task_scheduler 根据AppID查询已经完成的向量升级任务
	tableHistory := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTaskLog{}.TableName())
	var totalCountHistory int64
	err = tableHistory.Count(&totalCountHistory).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Count err: %+v", err)
		return nil, 0, err
	}
	var taskHistories []*entity.SchedulerTaskLog
	err = tableHistory.
		Where(query, entity.TaskUpgradeWorkflowVector).
		Order(order).
		Offset(offset).
		Limit(pageSize).
		Find(&taskHistories).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Find err: %+v", err)
		return nil, 0, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks success, total: %d, len(taskHistories): %d", totalCountHistory, len(taskHistories))
	// 合并tasks和taskHistories
	for _, taskHistory := range taskHistories {
		tasks = append(tasks, &entity.SchedulerTask{
			ID:             taskHistory.ID,
			UserID:         taskHistory.UserID,
			Type:           taskHistory.Type,
			Mutex:          taskHistory.Mutex,
			Params:         taskHistory.Params,
			RetryTimes:     taskHistory.RetryTimes,
			MaxRetryTimes:  taskHistory.MaxRetryTimes,
			Timeout:        taskHistory.Timeout,
			Runner:         taskHistory.Runner,
			RunnerInstance: taskHistory.RunnerInstance,
			Result:         taskHistory.Result,
			TraceID:        taskHistory.TraceID,
			StartTime:      taskHistory.StartTime,
			EndTime:        taskHistory.EndTime,
			NextStartTime:  taskHistory.NextStartTime,
			CreateTime:     taskHistory.CreateTime,
		})
	}
	totalCount += totalCountHistory
	return tasks, totalCount, nil
}

// GetUpgradeWorkflowVectorTaskByAppIDAndTraceID 获取升级工作流embedding向量任务
func GetUpgradeWorkflowVectorTaskByAppIDAndTraceID(ctx context.Context, appID string, traceID string) ([]*entity.SchedulerTask, error) {
	// task_scheduler 根据AppID查询向量升级任务
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID, appID: %s, traceID: %s", appID, traceID)
	if appID == "" && traceID == "" {
		return nil, nil
	}
	taskTable := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName())
	taskHistoryTable := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTaskLog{}.TableName())
	if appID != "" {
		appIDQuery := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.UserID)
		taskTable = taskTable.Where(appIDQuery, appID)
		taskHistoryTable = taskHistoryTable.Where(appIDQuery, appID)
	}
	if traceID != "" {
		traceIDQuery := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.TraceID)
		taskTable = taskTable.Where(traceIDQuery, traceID)
		taskHistoryTable = taskHistoryTable.Where(traceIDQuery, traceID)
	}
	// 查询未完成的任务
	var tasks []*entity.SchedulerTask
	err := taskTable.Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID success, len(task): %d", len(tasks))
	// 查询已完成的任务
	var taskHistories []*entity.SchedulerTaskLog
	err = taskHistoryTable.Find(&taskHistories).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID success, len(taskHistories): %d", len(taskHistories))
	// 合并tasks和taskHistories
	for _, taskHistory := range taskHistories {
		tasks = append(tasks, &entity.SchedulerTask{
			ID:             taskHistory.ID,
			UserID:         taskHistory.UserID,
			Type:           taskHistory.Type,
			Mutex:          taskHistory.Mutex,
			Params:         taskHistory.Params,
			RetryTimes:     taskHistory.RetryTimes,
			MaxRetryTimes:  taskHistory.MaxRetryTimes,
			Timeout:        taskHistory.Timeout,
			Runner:         taskHistory.Runner,
			RunnerInstance: taskHistory.RunnerInstance,
			Result:         taskHistory.Result,
			TraceID:        taskHistory.TraceID,
			StartTime:      taskHistory.StartTime,
			EndTime:        taskHistory.EndTime,
			NextStartTime:  taskHistory.NextStartTime,
			CreateTime:     taskHistory.CreateTime,
		})
	}
	return tasks, nil
}

// // StopUpgradeWorkflowVectorTaskByTaskID 停止升级工作流embedding向量任务
// func StopUpgradeWorkflowVectorTaskByTaskID(ctx context.Context, taskID uint64) error {
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID, taskID: %d", taskID)
// 	err := taskScheduler.StopTask(ctx, task_scheduler.TaskID(taskID))
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID failed, taskID:%d, err:%+v", taskID, err)
// 		return err
// 	}
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID success, taskID:%d", taskID)
// 	return nil
// }

// // StopUpgradeWorkflowVectorTaskByAppID 停止升级工作流embedding向量任务
// func StopUpgradeWorkflowVectorTaskByAppID(ctx context.Context, appID string) error {
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID, appID: %s", appID)
// 	task, err := GetUpgradeWorkflowVectorTaskByAppID(ctx, appID)
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID failed, appID:%s, err:%+v", appID, err)
// 		return err
// 	}
// 	taskID := task.ID
// 	err = taskScheduler.StopTask(ctx, task_scheduler.TaskID(taskID))
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID failed, appID:%s, taskID:%d, err:%+v", appID, taskID, err)
// 		return err
// 	}
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID success, appID:%s, taskID:%d", appID, taskID)
// 	return nil
// }

// RestartUpgradeWorkflowVectorTaskByAppID 重启升级工作流embedding向量任务
func RestartUpgradeWorkflowVectorTaskByAppID(ctx context.Context, appID string) error {
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID, appID: %s", appID)
	// 查询未完成的任务
	query := fmt.Sprintf("%s = %d AND %s = ?", entity.TSchedulerTaskColumns.Type, entity.TaskUpgradeWorkflowVector,
		entity.TSchedulerTaskColumns.UserID)
	var task entity.SchedulerTask
	err := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName()).
		Where(query, appID).
		Take(&task).Error
	if err != nil {
		log.ErrorContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return err
	}
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID get one task success, task: %+v", task)
	taskID := task.ID
	err = taskScheduler.ContinueTerminatedTask(ctx, task_scheduler.TaskID(taskID), task.RetryTimes, 0)
	if err != nil {
		log.ErrorContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID failed, appID:%s, taskID:%d, err:%+v", appID, taskID, err)
		return err
	}
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID success, appID:%s, taskID:%d", appID, taskID)
	return nil
}
