package embedding

import (
	"fmt"
	"net/http"
	"sort"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/go-comm/encode"
	jsoniter "github.com/json-iterator/go"
)

// v2.9.0 刷数据：升级工作流embedding向量相关接口
// ------------------------------------------------------------------
//
// 使用说明： 打开trpc-go的admin，增加自定义指令
//
// 1. 在trpc-go.yaml中 开启admin
//  1. 参考资料： https://iwiki.woa.com/p/99485663
//  2. `curl "http://ip:port/cmds"`
//  3. `curl "http://ip:port/version"`
//
// 2. 在服务中注册：
//  1. `import "git.code.oa.com/trpc-go/trpc-go/admin"`
//  2. `admin.HandleFunc("/test", r.s.Test)`
//  3. 七彩石 - trpc-go.yaml 里的admin里配置端口号
//
// 3. （可选）在节点中：`ss -tunlp` 查看端口和ip情况
//
// 4. 在节点: `curl -X POST -d "uin=aaa&robot_id=bbb" http://{ip}:{port}/test -v`
//
// ------------------------------------------------------------------
//
// 具体接口使用说明（key-value对）：
//   - all: 全量，取值范围：0 或者 1，可不填，默认为 0，避免误操作对线上全量数据造成影响
//     all = 0 指定应用，appid或percentage必填
//     all = 1 全部应用，appid或percentage可不填
//   - appid: 机器人ID （对应到t_robot表中的business_id），当 all 设置为 1 时可不填，否则必填
//   - percentage: 未升级的机器人ID集合的百分比，取值范围：0-100，当 all 设置为 1 时可不填，否则必填
//   - model: 指定embedding模型，需要为七彩石中囊括的模型
//   - test: 测试（不写库，仅跑逻辑），取值范围：0 或者 1，可不填，默认为 1，避免误操作对线上数据造成影响
//     test = 0 读数据并更新
//     test = 1 测试，只读数据
//
// 如：
//   - 刷全量： curl -X POST -d "all=1&scene=1&test=0" http://{ip}:{port}/embedding/xxx
//   - 刷单应用：curl -X POST -d "appid=123456&scene=1&test=0" http://{ip}:{port}/embedding/xxx
//   - 测试：curl -X POST -d "appid=123456&scene=2" http://{ip}:{port}/embedding/xxx

func UpgradeWorkflowVector(w http.ResponseWriter, r *http.Request) {
	respErr := func(w http.ResponseWriter, resp *UpgradeWorkflowVectorResp, errMsg string) {
		w.WriteHeader(http.StatusInternalServerError)
		resp.ErrMsg = errMsg
		respStr, _ := jsoniter.Marshal(resp)
		_, _ = w.Write(respStr)
	}

	// 1. 前置处理
	logPrefix := "embedding|UpgradeWorkflowVector"
	ctx := trpc.CloneContext(r.Context())
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	logFunctionPrefix := logPrefix
	resp := &UpgradeWorkflowVectorResp{}
	resp.TraceID = traceID
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 2. 参数处理
	// 2.1. 解析参数
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "%s|ParseForm|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	// 2.2. 提取参数
	all := false
	allStr := r.FormValue("all")
	if allStr != "" {
		all, err = strconv.ParseBool(allStr)
		if err != nil {
			log.WarnContextf(ctx, "%s|ParseBool|req:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "all is not a bool")
			return
		}
	}
	appID := r.FormValue("appid")
	if appID != "" {
		// 校验appID是否为无符号整数格式的字符串
		_, err := strconv.ParseUint(appID, 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "%s|appid parseUint failed, request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "appid is not a number")
			return
		}
	}
	percentage := 0
	percentageStr := r.FormValue("percentage")
	if percentageStr != "" {
		percentage, err = strconv.Atoi(percentageStr)
		if err != nil {
			log.ErrorContextf(ctx, "%s|percentage Atoi failed, request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "percentage is not a number")
			return
		}
		if percentage < 0 || percentage > 100 {
			log.ErrorContextf(ctx, "%s|percentage is out of range", logFunctionPrefix)
			respErr(w, resp, "percentage is out of range")
			return
		}
	}
	if !all && appID == "" && percentage == 0 {
		// 非全量刷数据时appID或percentage禁止为空
		log.ErrorContextf(ctx, "%s|all and appid/percentage is EMPTY", logFunctionPrefix)
		respErr(w, resp, "appid is EMPTY")
		return
	}
	modelName := r.FormValue("model")
	if modelName != "" {
		_, ok := config.GetMainConfig().WorkflowVectorGroup.EmbeddingVersionControl[modelName]
		if !ok {
			log.ErrorContextf(ctx, "%s|model %s not found in config", logFunctionPrefix, modelName)
			respErr(w, resp, "model not found in config")
			return
		}
	} else {
		log.ErrorContextf(ctx, "%s|model is EMPTY", logFunctionPrefix)
		respErr(w, resp, "model is EMPTY")
		return
	}
	test := true
	testStr := r.FormValue("test")
	if testStr != "" {
		test, err = strconv.ParseBool(testStr)
		if err != nil {
			log.ErrorContextf(ctx, "%s test ParseBool request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, err.Error())
			return
		}
	}
	// 3. 创建任务
	// 3.1. 整理appID
	appIDs := make([]string, 0)
	if !all && appID != "" {
		appIDs = append(appIDs, appID)
	} else {
		vdb := vdao.NewDao()
		db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
		needUpgradeAppIDs, err := vdb.GetNeedUpgradeWorkflowVectorAppIDs(ctx, db, entity.SaveWorkflowType, modelName)
		if err != nil {
			log.ErrorContextf(ctx, "%s|GetNeedUpgradeWorkflowVectorAppIDs|err:%+v", logFunctionPrefix, err)
			respErr(w, resp, err.Error())
			return
		}
		appIDs = needUpgradeAppIDs
		if !all && percentage != 0 {
			appIDs = appIDs[:int(float64(len(appIDs))*float64(percentage)/100)]
		}
	}
	log.InfoContextf(ctx, "%s|appIDs:%+v", logFunctionPrefix, appIDs)
	// 3.2. 创建任务参数
	taskParams := make([]entity.TaskUpgradeWorkflowVectorParams, 0)
	for _, appID := range appIDs {
		appInfo, err := rpc.GetAppInfo(ctx, entity.SandboxEnvScene, uint64(encode.StringToInt64(appID)))
		if err != nil {
			log.ErrorContextf(ctx, "%s|GetAppInfo|err:%+v", logFunctionPrefix, err)
			resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
				AppBizID: appID,
				ErrMsg:   err.Error(),
			})
			continue
		}
		taskParams = append(taskParams, entity.TaskUpgradeWorkflowVectorParams{
			RequestID:          traceID,
			Name:               appInfo.GetBaseConfig().Name,
			CorpID:             appInfo.GetCorpId(),
			RobotID:            appID,
			EmbeddingModelName: modelName,
			TestOnly:           test,
		})
	}
	// 3.3. 创建任务调度
	succeededAppIDs, failedAppIDs, failedErrors := scheduler.NewUpgradeWorkflowVectorTasks(ctx, taskParams)
	// 4. 响应
	for _, appID := range succeededAppIDs {
		resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
			AppBizID: appID,
			ErrMsg:   "",
		})
	}
	for i, appID := range failedAppIDs {
		resp.AppBizIDResults = append(resp.AppBizIDResults, &AppBizIDResult{
			AppBizID: appID,
			ErrMsg:   failedErrors[i].Error(),
		})
	}
	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "%s|resp:%s", logFunctionPrefix, string(respStr))
	_, _ = w.Write(respStr)
}

func GetUpgradeWorkflowVectorTasks(w http.ResponseWriter, r *http.Request) {
	respErr := func(w http.ResponseWriter, resp *GetUpgradeWorkflowVectorTasksResp, errMsg string) {
		w.WriteHeader(http.StatusInternalServerError)
		resp.ErrMsg = errMsg
		respStr, _ := jsoniter.Marshal(resp)
		_, _ = w.Write(respStr)
	}

	// 1. 前置处理
	logPrefix := "embedding|GetUpgradeWorkflowVectorTask"
	ctx := trpc.CloneContext(r.Context())
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	logFunctionPrefix := logPrefix
	resp := &GetUpgradeWorkflowVectorTasksResp{}
	resp.TraceID = traceID
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 2. 参数处理
	// 2.1. 解析参数
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "%s|ParseForm|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	// 2.2. 提取参数
	pageNum := 1
	pageNumStr := r.FormValue("page_num")
	if pageNumStr != "" {
		pageNum, err = strconv.Atoi(pageNumStr)
		if err != nil {
			log.ErrorContextf(ctx, "%s|pageNum Atoi failed, request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "page_num is not a number")
			return
		}
	}
	pageSize := 10
	pageSizeStr := r.FormValue("page_size")
	if pageSizeStr != "" {
		pageSize, err = strconv.Atoi(pageSizeStr)
		if err != nil {
			log.ErrorContextf(ctx, "%s|pageSize Atoi failed, request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "page_size is not a number")
			return
		}
	}
	// 3. 获取任务信息
	tasks, total, err := scheduler.GetUpgradeWorkflowVectorTasks(ctx, pageSize, pageNum)
	if err != nil {
		log.ErrorContextf(ctx, "%s|GetUpgradeWorkflowVectorTasks|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	resp.Total = int(total)
	for _, task := range tasks {
		resp.Tasks = append(resp.Tasks, AppBizIDTask{
			AppBizID:      fmt.Sprint(task.UserID),
			Params:        task.Params,
			Result:        task.Result,
			RetryTimes:    int(task.RetryTimes),
			MaxRetryTimes: int(task.MaxRetryTimes),
			Timeout:       int(task.Timeout),
			CreateTime:    task.CreateTime.Format("2006-01-02 15:04:05"),
			StartTime:     task.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:       task.EndTime.Format("2006-01-02 15:04:05"),
			NextStartTime: task.NextStartTime.Format("2006-01-02 15:04:05"),
		})
	}
	// 4. 响应
	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "%s|resp:%s", logFunctionPrefix, string(respStr))
	_, _ = w.Write(respStr)
}

func GetUpgradeWorkflowVectorTask(w http.ResponseWriter, r *http.Request) {
	respErr := func(w http.ResponseWriter, resp *GetUpgradeWorkflowVectorTaskResp, errMsg string) {
		w.WriteHeader(http.StatusInternalServerError)
		resp.ErrMsg = errMsg
		respStr, _ := jsoniter.Marshal(resp)
		_, _ = w.Write(respStr)
	}

	// 1. 前置处理
	logPrefix := "embedding|GetUpgradeWorkflowVectorTask"
	ctx := trpc.CloneContext(r.Context())
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	logFunctionPrefix := logPrefix
	resp := &GetUpgradeWorkflowVectorTaskResp{}
	resp.TraceID = traceID
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 2. 参数处理
	// 2.1. 解析参数
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "%s|ParseForm|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	// 2.2. 提取参数
	appID := r.FormValue("appid")
	if appID != "" {
		// 校验appID是否为无符号整数格式的字符串
		_, err := strconv.ParseUint(appID, 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "%s|appid parseUint failed, request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "appid is not a number")
			return
		}
	}
	taskTraceID := r.FormValue("traceid")
	if taskTraceID == "" && appID == "" {
		log.ErrorContextf(ctx, "%s|traceid or appid is empty, request:%+v", logFunctionPrefix, r)
		respErr(w, resp, "traceid or appid is EMPTY")
		return
	}
	// 3. 获取任务信息
	tasks, err := scheduler.GetUpgradeWorkflowVectorTaskByAppIDAndTraceID(ctx, appID, taskTraceID)
	if err != nil {
		log.ErrorContextf(ctx, "%s|GetUpgradeWorkflowVectorTaskByAppIDAndTraceID|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	if len(tasks) == 0 {
		log.ErrorContextf(ctx, "%s|GetUpgradeWorkflowVectorTaskByAppIDAndTraceID|err:task not found", logFunctionPrefix)
		respErr(w, resp, "task not found")
		return
	}
	// 按照CreateTime进行排序（最新创建的在前）
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].CreateTime.After(tasks[j].CreateTime)
	})
	resp.Tasks = make([]AppBizIDTask, 0, len(tasks))
	for _, task := range tasks {
		resp.Tasks = append(resp.Tasks, AppBizIDTask{
			AppBizID:      fmt.Sprint(task.UserID),
			Params:        task.Params,
			Result:        task.Result,
			RetryTimes:    int(task.RetryTimes),
			MaxRetryTimes: int(task.MaxRetryTimes),
			Timeout:       int(task.Timeout),
			CreateTime:    task.CreateTime.Format("2006-01-02 15:04:05"),
			StartTime:     task.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:       task.EndTime.Format("2006-01-02 15:04:05"),
			NextStartTime: task.NextStartTime.Format("2006-01-02 15:04:05"),
		})
	}
	// 4. 响应
	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "%s|resp:%s", logFunctionPrefix, string(respStr))
	_, _ = w.Write(respStr)
}

func RestartUpgradeWorkflowVector(w http.ResponseWriter, r *http.Request) {
	respErr := func(w http.ResponseWriter, resp *RestartUpgradeWorkflowVectorResp, errMsg string) {
		w.WriteHeader(http.StatusInternalServerError)
		resp.ErrMsg = errMsg
		respStr, _ := jsoniter.Marshal(resp)
		_, _ = w.Write(respStr)
	}

	// 1. 前置处理
	logPrefix := "embedding|RestartUpgradeWorkflowVector"
	ctx := trpc.CloneContext(r.Context())
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	logFunctionPrefix := logPrefix
	resp := &RestartUpgradeWorkflowVectorResp{}
	resp.TraceID = traceID
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 2. 参数处理
	// 2.1. 解析参数
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "%s|ParseForm|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	// 2.2. 提取参数
	appID := r.FormValue("appid")
	if appID != "" {
		// 校验appID是否为无符号整数格式的字符串
		_, err := strconv.ParseUint(appID, 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "%s|appid parseUint failed, request:%+v, err:%+v", logFunctionPrefix, r, err)
			respErr(w, resp, "appid is not a number")
			return
		}
	}
	resp.AppBizID = appID
	// 3. 重启任务
	err = scheduler.RestartUpgradeWorkflowVectorTaskByAppID(ctx, appID)
	if err != nil {
		log.ErrorContextf(ctx, "%s|GetUpgradeWorkflowVectorTaskByAppID|err:%+v", logFunctionPrefix, err)
		respErr(w, resp, err.Error())
		return
	}
	// 4. 响应
	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "%s|resp:%s", logFunctionPrefix, string(respStr))
	_, _ = w.Write(respStr)
}
