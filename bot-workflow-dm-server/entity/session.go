package entity

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

const (
	// SystemVariableQuery 系统变量query
	SystemVariableQuery = "SYS.UserQuery"
	// SystemVariableHistory 系统变量对话历史
	SystemVariableHistory = "SYS.ChatHistory"
	// SystemVariableTime 系统变量当前时间
	SystemVariableTime = "SYS.CurrentTime"
	// SystemVariableRewriteQuery query改写的结果
	SystemVariableRewriteQuery = "SYS.RewriteQuery"

	// SystemTimeFormat 系统变量当前时间格式
	SystemTimeFormat = "2006年01月02日 15:04:05"

	defaultDialogHistoryLimit = 15

	maxPromptHistoryLength   = 100000
	maxOneHistoryQueryLength = 100000

	// ForgedWorkflowID 构造的ID
	ForgedWorkflowID = "ForgedWorkflowID"
	// ForgedWorkflowName 构造的名称
	ForgedWorkflowName = "ForgedWorkflowName"
)

var (
	// SpeakerMap 发言人映射
	SpeakerMap = map[KEP_WF_DM.Role]string{
		KEP_WF_DM.Role_USER:      "用户输入",
		KEP_WF_DM.Role_ASSISTANT: "机器人回答",
	}
)

// DebugNode 调试中的节点
type DebugNode struct {
	NodeID    string
	Inputs    map[string]string
	ToolInput *KEP_WF_DM.ToolInputData
}

// Session 会话数据
type Session struct {
	// 请求传入的内容
	RunEnv            KEP_WF_DM.RunEnvType           // 运行环境
	SessionID         string                         // 会话ID
	AppID             string                         // 应用ID
	AppType           string                         // 应用类型
	Uin               uint64                         // 应用所属的Uin
	SID               uint32                         // 应用所属的集成商ID
	Query             string                         // 当前的query
	RelatedRecordID   string                         // 当前的query对应的RecordID
	RecordID          string                         // 当前的query的回复对应的RecordID
	RewriteQuery      string                         // 改写的query
	QueryHistory      []*KEP_WF_DM.Message           // 对话的内容
	customVariables   map[string]*KEP_WF_DM.Variable // API参数信息。
	Inputs            map[string]string              // 开始节点的输入变量信息。
	MainModelName     string                         // 主模型名称
	DebuggingNode     *DebugNode                     // 调试的节点
	ConfiguredHistory string                         // 配置的对话历史
	IsDebug           bool                           // 是否为调试中
	// LastQueryWorkflowID string                  // 上一个query命中的workflowID
	FinanceSubBizType string // 计费子业务类型

	// 配置缓存
	App *App
	// 运行时状态
	WorkflowRunStack         []*WorkflowRun
	HistoryRetainEndRecordID string               // 保留的对话历史结束位置 (HistoryRetainEndRecordID, HistoryDropEndRecordID] 间的历史会丢弃掉
	HistoryDropEndRecordID   string               // 丢弃的对话历史结束位置 (HistoryRetainEndRecordID, HistoryDropEndRecordID] 间的历史会丢弃掉
	backupHistory            []*KEP_WF_DM.Message // 备份的历史

	// 请求结果缓存
	ExpressionResults map[string]bool // 缓存表达式的结果，避免多次请求大模型

	// 临时的内容，不需要存储
	IsTerminated           bool `json:"-"` // 是否已经终止
	sync.RWMutex           `json:"-"`
	StatisticInfos         []*KEP_WF_DM.StatisticInfo `json:"-"` // 大模型token信息
	LLMResourceLimit       chan struct{}              `json:"-"` // 控制单个请求的LLM并发数
	HitOptionCardIndex     int32                      `json:"-"` // 命中选项卡的下标，从1开始
	AnswerOutput           map[string]any             `json:"-"` // 回复节点的自定义输出
	hasPassedParameterNode bool                       // 当前query是否经过参数提取节点，走完才算成功
	hasDealtParameterNode  bool                       // 当前query是否处理过参数节点
	successHolder          map[string]bool            // LLM的成功的Holder
	llmExclusive           map[string]bool            // 进行中LLM是否专属并发

	IsAsync           bool // 是否为异步请求
	workflowRunRecord *model.WorkflowRun
	nodeRunRecords    map[string]*model.NodeRun // Key：为 BelongNodeID + NodeID
}

// IsDebuggingNode 是否正在调试节点
func (s *Session) IsDebuggingNode() bool {
	return s.DebuggingNode != nil
}

// InitVar 初始化
func (s *Session) InitVar() {
	s.successHolder = make(map[string]bool)
	s.customVariables = make(map[string]*KEP_WF_DM.Variable)
	s.Inputs = make(map[string]string)
	s.LLMResourceLimit = make(chan struct{}, config.GetMainConfig().LLM.MaxConcurrencyPerRequest)
	s.llmExclusive = make(map[string]bool)
}

// RequestStart 请求开始的时候调用，初始化
func (s *Session) RequestStart(req *KEP_WF_DM.RunWorkflowRequest) {
	s.InitVar()
	s.Query = req.Query
	s.RelatedRecordID = req.RelatedRecordID
	s.RecordID = req.RecordID
	s.RewriteQuery = req.RewriteQuery
	s.MainModelName = req.MainModelName
	s.IsDebug = req.IsDebug
	// s.LastQueryWorkflowID = req.LastQueryWorkflowID
	// s.RewriteQuery = req.RewriteQuery
	if req.CustomVariables != nil {
		s.customVariables = req.CustomVariables
	}
	// 填充未赋值的API参数为默认值（需要在GetSession后）
	if s.App.Variables != nil {
		for _, variable := range s.App.Variables {
			if value := s.GetCustomVariable(variable.VarID); value == nil {
				if variable.VarDefaultValue != "" {
					// 填充到session
					s.customVariables[variable.VarName] = &KEP_WF_DM.Variable{
						Name:  variable.VarName,
						Value: variable.VarDefaultValue,
					}
				}
			}
		}
	}
	if req.Inputs != nil {
		s.Inputs = req.Inputs
	}

	s.QueryHistory = req.QueryHistory
	s.DoHistoryDrop()

	s.FinanceSubBizType = req.FinanceSubBizType
}

// AsyncStart 请求开始的时候调用，初始化
func (s *Session) AsyncStart(req *KEP_WF_DM.StartWorkflowRunRequest, workflowRun *model.WorkflowRun,
	nodeRuns []*model.NodeRun) {
	s.IsAsync = true

	s.InitVar()
	s.Query = req.Query
	s.RewriteQuery = req.Query
	s.MainModelName = req.MainModelName
	s.IsDebug = req.IsDebug
	if s.customVariables == nil {
		s.customVariables = make(map[string]*KEP_WF_DM.Variable)
	}
	for name, value := range req.CustomVariables {
		s.customVariables[name] = &KEP_WF_DM.Variable{
			Name:  name,
			Value: value,
		}
	}
	// 填充未赋值的API参数为默认值（需要在GetSession后）
	if s.App.Variables != nil {
		for _, variable := range s.App.Variables {
			if value := s.GetCustomVariable(variable.VarID); value == nil {
				if variable.VarDefaultValue != "" {
					// 填充到session
					s.customVariables[variable.VarName] = &KEP_WF_DM.Variable{
						Name:  variable.VarName,
						Value: variable.VarDefaultValue,
					}
				}
			}
		}
	}
	s.workflowRunRecord = workflowRun

	s.nodeRunRecords = make(map[string]*model.NodeRun)
	for _, nodeRun := range nodeRuns {
		key := GetNodeRunKey(nodeRun.BelongNodeID, nodeRun.NodeID)
		s.nodeRunRecords[key] = nodeRun
	}

	s.FinanceSubBizType = tconst.FinanceSubBizTypeAsyncWorkFlow
}

// StartDebugNode 调试节点开始的时候调用，初始化
func (s *Session) StartDebugNode(req *KEP_WF_DM.DebugWorkflowNodeDialogRequest, node *KEP_WF.WorkflowNode) {
	s.InitVar()
	s.Query = req.Query
	s.RelatedRecordID = req.RelatedRecordID
	s.RecordID = req.RecordID
	s.ConfiguredHistory = req.ConfiguredHistory
	s.RewriteQuery = req.RewriteQuery
	s.MainModelName = req.MainModelName
	// s.LastQueryWorkflowID = req.LastQueryWorkflowID
	// s.RewriteQuery = req.RewriteQuery
	// if req.Inputs != nil {
	//	s.Inputs = req.Inputs
	// }
	if req.CustomVariables != nil {
		s.customVariables = req.CustomVariables
	}
	// 填充未赋值的API参数为默认值（需要在GetSession后）
	if s.App.Variables != nil {
		for _, variable := range s.App.Variables {
			if value := s.GetCustomVariable(variable.VarID); value == nil {
				if variable.VarDefaultValue != "" {
					// 填充到session
					s.customVariables[variable.VarName] = &KEP_WF_DM.Variable{
						Name:  variable.VarName,
						Value: variable.VarDefaultValue,
					}
				}
			}
		}
	}

	s.QueryHistory = req.QueryHistory
	s.DoHistoryDrop()

	s.DebuggingNode = &DebugNode{
		NodeID:    node.NodeID,
		Inputs:    req.Inputs,
		ToolInput: nil,
	}

	// 构造一个假的workflow，用于调试
	s.App.Workflows[ForgedWorkflowID] = &KEP_WF.Workflow{
		WorkflowID: ForgedWorkflowID,
		Nodes:      []*KEP_WF.WorkflowNode{node},
	}
	s.InitEmptyWorkflowRun()
}

// SetHistoryRetainEnd 清除对话历史
func (s *Session) SetHistoryRetainEnd() {
	s.HistoryRetainEndRecordID = s.RelatedRecordID
	// 把当前的query移入到历史中
	s.QueryHistory = append(s.QueryHistory, &KEP_WF_DM.Message{Content: s.Query})
	s.Query = ""
}

// SetHistoryDropEnd 清除对话历史
func (s *Session) SetHistoryDropEnd() {
	s.HistoryDropEndRecordID = s.RelatedRecordID

	s.Query = ""
	retainEnd := -1
	for i := range s.QueryHistory {
		if s.QueryHistory[i].RecordID == s.HistoryRetainEndRecordID {
			retainEnd = i + 1
			continue
		}
	}
	if retainEnd >= 0 {
		s.QueryHistory = s.QueryHistory[0:retainEnd]
	} else {
		s.QueryHistory = make([]*KEP_WF_DM.Message, 0)
	}
}

// DoHistoryDrop 删除部分对话历史
func (s *Session) DoHistoryDrop() {
	s.backupHistory = append(s.QueryHistory, &KEP_WF_DM.Message{Content: s.Query})
	if s.HistoryRetainEndRecordID != "" {
		retainEnd, dropEnd := -1, -1
		for i := range s.QueryHistory {
			if s.QueryHistory[i].RecordID == s.HistoryRetainEndRecordID {
				retainEnd = i
				continue
			}
			if s.QueryHistory[i].RecordID == s.HistoryDropEndRecordID {
				dropEnd = i
				break
			}
		}
		if retainEnd >= 0 && retainEnd < dropEnd {
			s.QueryHistory = append(s.QueryHistory[0:retainEnd+1], s.QueryHistory[dropEnd+1:]...)
		} else if dropEnd > 0 {
			s.QueryHistory = s.QueryHistory[dropEnd+1:]
		}
	}
}

// ClearHistoryDrop 清除对话历史的删除，恢复原来的内容
func (s *Session) ClearHistoryDrop() {
	s.HistoryRetainEndRecordID = ""
	s.HistoryDropEndRecordID = ""
	s.QueryHistory = s.backupHistory
}

// // CompleteDropHistory 完成时清除对话历史
// func (s *Session) CompleteDropHistory() {
//	s.DropHistoryRecordID = s.RecordID
// }

// RequestEnd 请求结束的时候调用
func (s *Session) RequestEnd() {
	// 任务结束（全成功or有一个节点失败）时，把workflowRun出栈
	workflowRun := s.GetCurWorkflowRun()
	if workflowRun.IsFinished() {
		s.WorkflowRunStack = s.WorkflowRunStack[1:]
		// s.CompleteDropHistory()
	}
}

// // GetVariable 获取自定义参数。
// func (s *Session) GetVariable(varID string) *KEP_WF_DM.Var {
//	if s.App == nil {
//		return nil
//	}
//	variable, ok := s.App.Variables[varID]
//	if !ok {
//		return nil
//	}
//	return variable
// }

// JudgeExpression 判断表达式是否成立
func (s *Session) JudgeExpression(expression *ExpressionInfo) (result, existed bool) {
	s.RLock()
	defer s.RUnlock()
	if s.ExpressionResults == nil {
		return false, false
	}
	result, existed = s.ExpressionResults[expression.String()]
	return
}

// SetExpressionCache 设置表达式结果
func (s *Session) SetExpressionCache(expressions []*ExpressionInfo, results []bool) {
	s.Lock()
	defer s.Unlock()
	if s.ExpressionResults == nil {
		s.ExpressionResults = make(map[string]bool)
	}
	if len(expressions) != len(results) {
		return
	}
	for i, expression := range expressions {
		s.ExpressionResults[expression.String()] = results[i]
	}
}

// // AddStatisticInfoToSession 添加统计信息到session
// func AddStatisticInfoToSession(ctx context.Context, statistic *llmm.StatisticInfo) {
//	if statistic == nil {
//		return
//	}
//	if session, ok := ctx.Value(tconst.SessionKey).(*Session); ok && session != nil {
//		session.StatisticInfos = append(session.StatisticInfos, &KEP_WF_DM.StatisticInfo{
//			FirstTokenCost: statistic.FirstTokenCost,
//			TotalCost:      statistic.TotalCost,
//			InputTokens:    statistic.InputTokens,
//			OutputTokens:   statistic.OutputTokens,
//			TotalTokens:    statistic.TotalTokens,
//		})
//	}
// }

// GetNodeOutput 获取节点的输出的值。
func (s *Session) GetNodeOutput(belongNodeID, nodeID string, jsonPath string) interface{} {
	nodeRun := s.GetCurWorkflowRun().GetNodeRun(belongNodeID, nodeID)
	if nodeRun == nil {
		return nil
	}
	// 把jsonPath的Output.删除，然后解析数据
	switch jsonPath {
	case OutputField:
		jsonPath = ""
	case ErrorField:
		return map[string]interface{}{
			"FailCode":    nodeRun.ErrorCode,
			"FailMessage": nodeRun.FailMessage,
		}
	case ErrorCodeField:
		return nodeRun.ErrorCode
	case ErrorMessageField:
		return nodeRun.FailMessage
	default:
		jsonPath = strings.TrimPrefix(jsonPath, OutputField+".")
	}
	return util.GetValueByPath(nodeRun.Output, jsonPath)
}

// GetNodeReferences 获取节点的参考来源信息。
func (s *Session) GetNodeReferences(belongNodeID, nodeID string, jsonPath string) []*KEP_WF_DM.Reference {
	nodeRun := s.GetCurWorkflowRun().GetNodeRun(belongNodeID, nodeID)
	return nodeRun.GetReferencesByPath(jsonPath)
}

// GetSystemVariable 获取系统变量的值。
func (s *Session) GetSystemVariable(systemVariable *KEP_WF.SystemVariable) string {
	switch systemVariable.GetName() {
	case SystemVariableQuery:
		return s.Query
	case SystemVariableRewriteQuery:
		return s.RewriteQuery
	case SystemVariableHistory:
		// allHistory := append(s.QueryHistory, &KEP_WF_DM.Message{Content: s.Query})
		allHistory := s.QueryHistory
		roundLimit := int(systemVariable.DialogHistoryLimit)
		if roundLimit == 0 {
			roundLimit = defaultDialogHistoryLimit
		}
		main, last := s.GetHistoryDescribe(allHistory, maxPromptHistoryLength, maxOneHistoryQueryLength, roundLimit)
		return main + last
	case SystemVariableTime:
		return time.Now().Format(SystemTimeFormat)
	default:
		return ""
	}
}

// GetHistoryDescribe 获取历史对话的内容
func (s *Session) GetHistoryDescribe(history []*KEP_WF_DM.Message, maxLength, oneMaxLength, roundLimit int) (main,
	last string) {
	if len(history) == 0 {
		return "", ""
	}
	i := len(history) - 1
	if history[i].Role == KEP_WF_DM.Role_ASSISTANT {
		content := []rune(history[i].Content)
		if len(content) > oneMaxLength {
			content = content[:oneMaxLength]
		}
		last = fmt.Sprintf("%s：%s", SpeakerMap[history[i].Role], string(content))
		i--
	}
	roundCount := 0
	for ; i >= 0; i-- {
		content := []rune(history[i].Content)
		if len(content) > oneMaxLength {
			content = content[:oneMaxLength]
		}
		oneDesc := fmt.Sprintf("%s：%s\n", SpeakerMap[history[i].Role], string(content))
		if len([]rune(oneDesc+main)) > maxLength {
			return main, last
		}
		main = oneDesc + main
		// 轮数限制
		if history[i].Role == KEP_WF_DM.Role_USER {
			roundCount++
			if roundLimit > 0 && roundCount >= roundLimit {
				return
			}
		}
	}
	return main, last
}

// GetCustomVariable 获取自定义变量的值
func (s *Session) GetCustomVariable(customVarID string) any {
	if s.App.Variables == nil {
		return nil
	}
	s.RLock()
	defer s.RUnlock()
	variable := s.App.Variables[customVarID]
	if variable == nil {
		return nil
	}
	varValue := s.customVariables[variable.VarName]
	if varValue == nil {
		return nil
	}
	convertedVal, err := util.ConvertValue(varValue.GetValue(), variable.GetValueType())
	if err != nil {
		return nil
	}
	return convertedVal
}

// ConsumeLLMResource 消费LLM资源。单个LLM资源的并发限制
func (s *Session) ConsumeLLMResource(ctx context.Context) {
	select {
	case <-ctx.Done():
	case s.LLMResourceLimit <- struct{}{}:
	}
}

// ReleaseLLMResource 释放LLM资源
func (s *Session) ReleaseLLMResource(ctx context.Context) {
	select {
	case <-ctx.Done():
	case <-s.LLMResourceLimit:
	}
}

// GetWorkflow 根据ID获取工作流
func (s *Session) GetWorkflow(workflowID string) *KEP_WF.Workflow {
	s.RLock()
	defer s.RUnlock()
	if s.App == nil || s.App.Workflows == nil {
		return nil
	}
	return s.App.Workflows[workflowID]
}

// GetWorkflowStartNode 根据工作流ID获取开始节点
func (s *Session) GetWorkflowStartNode(workflowID string) *KEP_WF.WorkflowNode {
	s.RLock()
	defer s.RUnlock()
	if s.App == nil || s.App.Workflows == nil {
		return nil
	}
	for _, node := range s.App.Workflows[workflowID].GetNodes() {
		if node.NodeType == KEP_WF.NodeType_START {
			return node
		}
	}
	return nil
}

// // GetWorkflowEndNode 根据工作流ID获取结束节点
// func (s *Session) GetWorkflowEndNode(workflowID string) *KEP_WF.WorkflowNode {
//	if s.App == nil || s.App.Workflows == nil {
//		return nil
//	}
//	for _, node := range s.App.Workflows[workflowID].GetNodes() {
//		if node.NodeType == KEP_WF.NodeType_END {
//			return node
//		}
//	}
//	return nil
// }

// SetWorkflow 设置workflow
func (s *Session) SetWorkflow(workflowID string, workflow *KEP_WF.Workflow) {
	s.Lock()
	defer s.Unlock()
	if s.App == nil {
		return
	}
	if s.App.Workflows == nil {
		s.App.Workflows = make(map[string]*KEP_WF.Workflow)
	}
	s.App.Workflows[workflowID] = workflow
}

// GetWorkflowNode 根据ID获取当前工作流节点
func (s *Session) GetWorkflowNode(nodeID string) *KEP_WF.WorkflowNode {
	if s.App == nil || s.App.Workflows == nil {
		return nil
	}
	currentWorkflow := s.GetCurWorkflowRun()
	if currentWorkflow == nil {
		return nil
	}
	s.RLock()
	defer s.RUnlock()
	curWorkflow := s.App.Workflows[currentWorkflow.WorkflowID]
	for _, node := range curWorkflow.GetNodes() {
		if node.GetNodeID() == nodeID {
			return node
		}
	}
	for _, workflow := range s.App.Workflows {
		if curWorkflow.GetWorkflowID() == workflow.GetWorkflowID() {
			continue
		}
		for _, node := range workflow.GetNodes() {
			if node.GetNodeID() == nodeID {
				return node
			}
		}
	}
	return nil
}

// GetApiParameter 根据ID获取参数
func (s *Session) GetApiParameter(parameterID string) *KEP_WF_DM.Parameter {
	s.RLock()
	defer s.RUnlock()
	if s.App == nil {
		return nil
	}
	return s.App.Parameters[parameterID]
}

// // SetApiParameter 根据ID获取参数
// func (s *Session) SetApiParameter(parameterID string, parameter *KEP_WF_DM.Parameter) {
//	s.Lock()
//	defer s.Unlock()
//	if s.App == nil || s.App.Parameters == nil {
//		return
//	}
//	s.App.Parameters[parameterID] = parameter
// }

// InitWorkflowRun 初始化工作流的运行
func (s *Session) InitWorkflowRun(workflowID, workflowName, workflowReleaseTime string) *WorkflowRun {
	index := -1
	for i := range s.WorkflowRunStack {
		if workflowID == s.WorkflowRunStack[i].WorkflowID {
			index = i
			break
		}
	}
	if index > 0 {
		// 意图置顶
		tmpWorkflowRun := s.WorkflowRunStack[index]
		tmpIntentStack := append(s.WorkflowRunStack[:index], s.WorkflowRunStack[index+1:]...)
		s.WorkflowRunStack = append([]*WorkflowRun{tmpWorkflowRun}, tmpIntentStack...)
	} else if index < 0 {
		// 新意图： 入栈，保持长度
		workflowRunID := GenWorkflowRunID()
		if record := s.GetWorkflowRunRecord(); s.IsAsync && record != nil {
			workflowRunID = record.WorkflowRunID
		}
		curWorkflowRun := NewWorkflowRun(workflowRunID, workflowID, workflowName, workflowReleaseTime)
		s.WorkflowRunStack = append([]*WorkflowRun{curWorkflowRun}, s.WorkflowRunStack...)
		maxStackSize := config.GetMainConfig().Workflow.WorkflowStackMaxLength
		if maxStackSize < 1 {
			maxStackSize = 1
		}
		if len(s.WorkflowRunStack) > maxStackSize {
			s.WorkflowRunStack = s.WorkflowRunStack[:maxStackSize]
		}
	}

	return s.WorkflowRunStack[0]
}

// InitEmptyWorkflowRun 初始化空的工作流的运行
func (s *Session) InitEmptyWorkflowRun() {
	if len(s.WorkflowRunStack) == 0 {
		s.WorkflowRunStack = []*WorkflowRun{NewWorkflowRun(ForgedWorkflowID, ForgedWorkflowID, ForgedWorkflowName,
			time.Now().Format(time.RFC3339))}
	}
}

// GetCurWorkflowRun 获取工作流当前的运行
func (s *Session) GetCurWorkflowRun() *WorkflowRun {
	if len(s.WorkflowRunStack) == 0 {
		return nil
	}
	return s.WorkflowRunStack[0]
}

// SetTerminated 设置对话是否结束
func (s *Session) SetTerminated() {
	s.IsTerminated = true
}

// GetOrgWorkflowID 获取嵌套工作流的源工作流
func (s *Session) GetOrgWorkflowID(workflowID string) string {
	for _, workflowRun := range s.WorkflowRunStack {
		// 不需要 workflowRun.WorkflowID == workflowID || ，因为如果进了父工作流的子工作流A，就不应该再切换到 A 了。栈顶的优先级更高
		if workflowRun.IsRunningSubWorkflow(workflowID) {
			return workflowRun.WorkflowID
		}
	}
	return workflowID
}

// HasPassedParameterNode 当前query是否有经过参数提取节点
func (s *Session) HasPassedParameterNode() bool {
	return s.hasPassedParameterNode
}

// SetPassedParameterNode 设置当前query经过参数提取节点
func (s *Session) SetPassedParameterNode() {
	s.hasPassedParameterNode = true
}

// HasDealtParameterNode 当前query是否处理过参数节点
func (s *Session) HasDealtParameterNode() bool {
	return s.hasDealtParameterNode
}

// SetDealtParameterNode 设置当前query处理过参数节点
func (s *Session) SetDealtParameterNode() {
	s.hasDealtParameterNode = true
}

// // ConvertMainModelName 转换主模型名称
// func (s *Session) ConvertMainModelName(modelName string) string {
//	if modelName == config.GetMainConfig().Model.DefaultModelName {
//		return s.MainModelName
//	}
//	return modelName
// }

// IsMainModelName 判断是否为主模型
func (s *Session) IsMainModelName(modelName string) bool {
	return modelName == s.MainModelName
}

// SetLLMSuccessHolder 设置模型的成功holder
func (s *Session) SetLLMSuccessHolder(holder string) {
	s.Lock()
	defer s.Unlock()
	s.successHolder[holder] = true
}

// IsSuccessHolder 判断模型的成功holder
func (s *Session) IsSuccessHolder(holder string) bool {
	s.RLock()
	defer s.RUnlock()
	return s.successHolder[holder]
}

// SetLLMExclusive 设置专属并发的模型
func (s *Session) SetLLMExclusive(modelName string) {
	s.Lock()
	defer s.Unlock()
	s.llmExclusive[modelName] = true
}

// IsLLMExclusive 判断模型是否为专属并发
func (s *Session) IsLLMExclusive(modelName string) bool {
	s.RLock()
	defer s.RUnlock()
	return s.llmExclusive[modelName]
}

// AddNewParameters 添加新的参数
func (s *Session) AddNewParameters(parameters map[string]*KEP_WF_DM.Parameter) {
	s.Lock()
	defer s.Unlock()
	if s.App == nil {
		return
	}
	if s.App.Parameters == nil {
		s.App.Parameters = make(map[string]*KEP_WF_DM.Parameter)
	}
	for pID, parameter := range parameters {
		if _, ok := s.App.Parameters[pID]; !ok {
			s.App.Parameters[pID] = parameter
		}
	}
}

// AddNewVariables 添加新的变量
func (s *Session) AddNewVariables(variables map[string]*KEP_WF_DM.Var) {
	s.Lock()
	defer s.Unlock()
	if s.App == nil {
		return
	}
	if s.App.Variables == nil {
		s.App.Variables = make(map[string]*KEP_WF_DM.Var)
	}
	for vID, variable := range variables {
		if _, ok := s.App.Variables[vID]; !ok {
			s.App.Variables[vID] = variable
		}
	}
}

// SetHitOptionCardIndex 设置命中的选卡下标（从1开始）
func (s *Session) SetHitOptionCardIndex(hitOptionCardIndex int32) {
	s.HitOptionCardIndex = hitOptionCardIndex
}

// SetAnswerOutput 设置回复节点输出变量
func (s *Session) SetAnswerOutput(answerOutput map[string]any) {
	s.AnswerOutput = answerOutput
}

// // IsContinuous 是不是连续的工作流对话
// func (s *Session) IsContinuous() bool {
//	if s.GetCurWorkflowRun() == nil {
//		return false
//	}
//	return s.GetCurWorkflowRun().WorkflowID == s.LastQueryWorkflowID
// }

// GetWorkflowRunRecord 获取工作流运行记录
func (s *Session) GetWorkflowRunRecord() *model.WorkflowRun {
	s.RLock()
	defer s.RUnlock()
	return s.workflowRunRecord
}

// SetWorkflowRunRecord 设置工作流运行记录
func (s *Session) SetWorkflowRunRecord(record *model.WorkflowRun) {
	s.Lock()
	defer s.Unlock()
	s.workflowRunRecord = record
}

// GetNodeRunRecords 获取节点运行记录
func (s *Session) GetNodeRunRecords() map[string]*model.NodeRun {
	s.RLock()
	defer s.RUnlock()
	return s.nodeRunRecords
}

// SetNodeRunRecords 设置节点运行记录
func (s *Session) SetNodeRunRecords(records map[string]*model.NodeRun) {
	s.Lock()
	defer s.Unlock()
	s.nodeRunRecords = records
}

// GetNodeRunRecord 获取单个节点运行记录
func (s *Session) GetNodeRunRecord(key string) *model.NodeRun {
	s.RLock()
	defer s.RUnlock()
	return s.nodeRunRecords[key]
}

// SetNodeRunRecord 设置单个节点运行记录
func (s *Session) SetNodeRunRecord(key string, record *model.NodeRun) {
	s.Lock()
	defer s.Unlock()
	if s.nodeRunRecords == nil {
		s.nodeRunRecords = make(map[string]*model.NodeRun)
	}
	s.nodeRunRecords[key] = record
}
